import { Typography } from '@components/typography';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@components/ui/carousel';
import { useIsMobileView } from '@hooks/system';
import { useIntersectionObserver } from '@hooks/system/use-intersection-observer';
import { cn } from '@utils/tailwind';
import { uniqBy } from 'lodash';
import { useEffect, useLayoutEffect, useRef } from 'react';

type DealsFeatureCarouselProps<T extends { id?: string | number }> = {
  cards: T[];
  title: React.ReactNode;
  actionBtn?: React.ReactNode;
  renderCard: (card: T) => React.ReactNode;
  hasNextPage?: boolean;
  fetchNextPage?: () => void; // Callback to fetch more items
  isFetchingNextPage?: boolean;
};

export const CardsCarousel = <T extends { id?: string | number }>({
  title,
  cards,
  actionBtn,
  renderCard,
  hasNextPage,
  fetchNextPage,
  isFetchingNextPage,
}: DealsFeatureCarouselProps<T>) => {
  const isMobileView = useIsMobileView();
  const carouselApiRef = useRef<CarouselApi>();
  const selectedIndexRef = useRef<number>(0); // Store the currently visible slide index
  const previousCardsLengthRef = useRef(cards.length); // Move this declaration up

  // Use the custom useIntersectionObserver hook for lazy loading
  const { ref: triggerRef, isIntersecting } = useIntersectionObserver({
    rootMargin: '200px', // Trigger when the sentinel is 200px from the viewport edge
    threshold: 0.01, // Trigger when 1% of the sentinel is visible
  });

  // Track the selected slide index
  useLayoutEffect(() => {
    const api = carouselApiRef.current;
    if (!api) return;

    const updateSelectedIndex = () => {
      selectedIndexRef.current = api.selectedScrollSnap();
    };

    api.on('select', updateSelectedIndex);
    api.on('reInit', updateSelectedIndex);

    // Initial update
    updateSelectedIndex();

    return () => {
      api.off('select', updateSelectedIndex);
      api.off('reInit', updateSelectedIndex);
    };
  }, [carouselApiRef.current]);

  // Restore scroll position after new items are added
  // useLayoutEffect(() => {
  //   const api = carouselApiRef.current;
  //   if (!api) return;

  //   // If new items were added
  //   if (cards.length > previousCardsLengthRef.current) {
  //     // Reinitialize carousel to account for new slides
  //     api.reInit();

  //     // Restore the previous scroll position
  //     requestAnimationFrame(() => {
  //       api.scrollTo(selectedIndexRef.current, false); // Scroll to the previously selected slide
  //     });
  //   }

  //   previousCardsLengthRef.current = cards.length;
  // }, [cards.length]);

  // Trigger lazy loading when the sentinel element is intersecting
  useEffect(() => {
    if (isIntersecting && hasNextPage && !isFetchingNextPage) {
      fetchNextPage?.();
    }
  }, [isIntersecting, hasNextPage, isFetchingNextPage, fetchNextPage]);

  if (!cards?.length) {
    return null;
  }

  const finalCards = uniqBy(cards, 'id');

  return (
    <Carousel
      className={'w-full overflow-hidden'}
      opts={{
        loop: false, // Disable looping to avoid issues with lazy loading
        align: 'start',
        dragFree: true,
      }}
      setApi={(api) => {
        carouselApiRef.current = api;
      }}
    >
      <div
        className={'mb-6 md:mb-2 grid grid-flow-col items-center px-6 md:px-0'}
      >
        <Typography
          tag="h2"
          variant={isMobileView ? 'xxs' : 'xs'}
          className={'flex items-center gap-3'}
        >
          {title}
        </Typography>

        <div className={'relative ml-auto h-8'}>
          {actionBtn}

          {!isMobileView && (
            <>
              <CarouselPrevious
                className={cn(
                  actionBtn ? '!-left-[5.7rem]' : '!-left-[4.7rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50',
                )}
              />
              <CarouselNext
                className={cn(
                  actionBtn ? '!-left-[3rem]' : '!-left-[2rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50',
                )}
              />
            </>
          )}
        </div>
      </div>
      <CarouselContent
        isInteractive
        containerClassName="pl-6 md:pl-0"
        className="-ml-6 mr-5 flex py-4"
      >
        {finalCards.map((card, index) => (
          <CarouselItem
            key={card.id ? `card-${card.id}` : `card-index-${index}`}
            className="basis-[15.75rem] pl-6"
          >
            {renderCard(card)}
          </CarouselItem>
        ))}

        {hasNextPage && (
          <CarouselItem key="loading-trigger" className="basis-auto pl-6">
            <div
              ref={triggerRef}
              className="flex h-full w-16 items-center justify-center"
              style={{
                minWidth: '64px',
                visibility: 'visible',
                display: 'flex',
              }}
              data-testid="horizontal-infinite-scroll-trigger"
            >
              {isFetchingNextPage && (
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
              )}
            </div>
          </CarouselItem>
        )}
      </CarouselContent>
    </Carousel>
  );
};
