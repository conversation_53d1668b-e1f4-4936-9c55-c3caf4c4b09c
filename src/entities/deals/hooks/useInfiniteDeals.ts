import { LANGUAGE_ABBREVIATION_BY_SHORTNAME } from '@entities/languages';
import type { Deal } from '@pages/deals/types';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DealStatus } from '@/shared/types';

import { type DealsQueryVariables, useDealsQuery } from '../api/queries.gen';

export interface UseInfiniteDealsOptions {
  variables?: Omit<DealsQueryVariables, 'page' | 'limit'>;
  limit?: number;
  enabled?: boolean;
}

export interface UseInfiniteDealsResult {
  data: Deal[];
  isLoading: boolean;
  isFetchingNextPage: boolean;
  hasNextPage: boolean;
  error: Error | null;
  fetchNextPage: () => void;
  currentPage: number;
  total: number;
}

export const useInfiniteDeals = (
  options: UseInfiniteDealsOptions = {},
): UseInfiniteDealsResult => {
  const { variables = {}, limit = 20, enabled = true } = options;
  const { i18n } = useTranslation();

  const [currentPage, setCurrentPage] = useState(1);
  // const [allDeals, setAllDeals] = useState<Deal[]>([]);
  const dealsRef = useRef<Deal[]>([]);
  const [isFetchingNextPage, setIsFetchingNextPage] = useState(false);

  const queryVariables: DealsQueryVariables = useMemo(
    () => ({
      ...variables,
      statuses: [DealStatus.ACTIVE],
      page: currentPage,
      limit,
    }),
    [variables, currentPage, limit],
  );

  const { data, isLoading, error, refetch } = useDealsQuery(queryVariables, {
    enabled,
  });

  useEffect(() => {
    if (data?.deals?.data) {
      const transformedDeals = data.deals.data
        .map((deal) => {
          const translations = deal?.translations?.find(
            (translation) =>
              translation?.language ===
              LANGUAGE_ABBREVIATION_BY_SHORTNAME[i18n.language],
          );
          const dealTitle = translations?.deal_title;
          const dealDescription = translations?.description;

          return {
            id: deal?.id,
            title: dealTitle,
            imageUrl: deal?.image_url,
            imagePath: deal?.image_path,
            categoryName: deal?.category_name,
            description: dealDescription,
            merchantName: deal?.merchant?.name,
            merchantLogoPath: deal?.merchant?.deals_logo_url,
            featured: deal?.featured,
            discountLabel: translations?.discount_label,
          };
        })
        .filter((deal) => deal.id !== undefined && deal.title !== undefined);

      if (currentPage === 1) {
        dealsRef.current = transformedDeals;
      } else {
        dealsRef.current = [...dealsRef.current, ...transformedDeals];
      }
      setIsFetchingNextPage(false);
    }
  }, [data, currentPage, i18n.language]);

  const hasNextPage = data?.deals?.has_more_pages ?? false;
  const total = data?.deals?.total ?? 0;

  useEffect(() => {
    if (currentPage > 1) {
      refetch();
    }
  }, [currentPage, refetch]);

  const fetchNextPage = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && !isLoading) {
      setIsFetchingNextPage(true);
      setCurrentPage((prev) => prev + 1);
    }
  }, [hasNextPage, isFetchingNextPage, isLoading, currentPage]);

  const variablesKey = JSON.stringify(variables);
  useEffect(() => {
    setCurrentPage(1);
    dealsRef.current = [];
    setIsFetchingNextPage(false);
  }, [variablesKey, enabled]);

  return {
    data: dealsRef.current,
    isLoading: isLoading && currentPage === 1,
    isFetchingNextPage,
    hasNextPage,
    error: error as Error | null,
    fetchNextPage,
    currentPage,
    total,
  };
};
